import React, { useEffect, useState } from 'react';
import './AdminDashboard.css';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaChartPie, FaListUl, FaSignOutAlt, FaCrown, FaShieldAlt } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

const AdminDashboard = () => {
  const [stats, setStats] = useState(null);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchStats = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('No token found. Please log in.');
        return;
      }
      try {
        const response = await fetch('http://localhost:5000/api/admin/dashboard', {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        const data = await response.json();
        if (response.ok) {
          setStats(data);
        } else {
          setError(data.message || 'Failed to fetch stats');
        }
      } catch (err) {
        setError('Network error');
      }
    };
    fetchStats();
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/');
  };

  if (error) return <div className="dashboard-error">{error}</div>;
  if (!stats) return <div className="dashboard-loading">Loading dashboard...</div>;

  return (
    <div className="dashboard-container">
      <button className="dashboard-logout-btn" onClick={handleLogout} title="Logout">
        <FaSignOutAlt />Logout
      </button>
      <h2 className="dashboard-title">
        <FaCrown style={{color: '#FF2D55'}}/>
        Admin Dashboard
        <FaShieldAlt style={{color: '#4B0082'}}/>
      </h2>
      <div className="dashboard-stats-grid">
        <div className="dashboard-stat-card">
          <FaEye className="dashboard-stat-icon" style={{color: '#4B0082'}}/>
          <div className="dashboard-stat-label">Total Visits</div>
          <div className="dashboard-stat-value">{stats.totalVisits}</div>
        </div>
        <div className="dashboard-stat-card dashboard-section-stats">
          <FaChartPie className="dashboard-stat-icon" style={{color: '#FF2D55'}}/>
          <div className="dashboard-stat-label">Section Stats</div>
          <div className="dashboard-section-stats-grid">
            {stats.sectionStats.map((s, i) => (
              <div className="dashboard-section-stat" key={i}>
                <span className="section-name">{s.section}</span>
                <span className="section-count">{s.count} views</span>
                <span className="section-percent">({s.percent}%)</span>
              </div>
            ))}
          </div>
        </div>
        <div className="dashboard-stat-card dashboard-visitor-log">
          <FaListUl className="dashboard-stat-icon" style={{color: '#4B0082'}}/>
          <div className="dashboard-stat-label">Visitor Log</div>
          <div className="dashboard-visitor-log-list">
            {stats.visits.length === 0 ? (
              <div className="dashboard-empty">No visits yet.</div>
            ) : (
              <ul>
                {stats.visits.map((v, i) => (
                  <li key={i}>
                    <span className="visitor-timestamp">{new Date(v.timestamp).toLocaleString()}</span>
                    <span className="visitor-ip">{v.ip}</span>
                    <span className="visitor-section">{v.section}</span>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard; 