{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchStats = async () => {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setError('No token found. Please log in.');\n        return;\n      }\n      try {\n        const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        const data = await response.json();\n        if (response.ok) {\n          setStats(data);\n        } else {\n          setError(data.message || 'Failed to fetch stats');\n        }\n      } catch (err) {\n        setError('Network error');\n      }\n    };\n    fetchStats();\n  }, []);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      color: 'red',\n      margin: 20\n    },\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 21\n  }, this);\n  if (!stats) return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      margin: 20\n    },\n    children: \"Loading dashboard...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 22\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      margin: 20\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Admin Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n        children: \"Total Visits:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 12\n      }, this), \" \", stats.totalVisits]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Visitor Log\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: stats.visits.map((v, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [v.timestamp, \" \\u2014 \", v.ip, \" \\u2014 \", v.section]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Section Stats\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: stats.sectionStats.map((s, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: [s.section, \": \", s.count, \" views (\", s.percent, \"%)\"]\n      }, i, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"EbJQS5GpWNuLdr0Eg+eRk+n8dU0=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "stats", "setStats", "error", "setError", "fetchStats", "token", "localStorage", "getItem", "response", "fetch", "headers", "data", "json", "ok", "message", "err", "style", "color", "margin", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalVisits", "visits", "map", "v", "i", "timestamp", "ip", "section", "sectionStats", "s", "count", "percent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/AdminDashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\n\r\nconst AdminDashboard = () => {\r\n  const [stats, setStats] = useState(null);\r\n  const [error, setError] = useState('');\r\n\r\n  useEffect(() => {\r\n    const fetchStats = async () => {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        setError('No token found. Please log in.');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await fetch('http://localhost:5000/api/admin/dashboard', {\r\n          headers: { 'Authorization': `Bearer ${token}` }\r\n        });\r\n        const data = await response.json();\r\n        if (response.ok) {\r\n          setStats(data);\r\n        } else {\r\n          setError(data.message || 'Failed to fetch stats');\r\n        }\r\n      } catch (err) {\r\n        setError('Network error');\r\n      }\r\n    };\r\n    fetchStats();\r\n  }, []);\r\n\r\n  if (error) return <div style={{ color: 'red', margin: 20 }}>{error}</div>;\r\n  if (!stats) return <div style={{ margin: 20 }}>Loading dashboard...</div>;\r\n\r\n  return (\r\n    <div style={{ margin: 20 }}>\r\n      <h2>Admin Dashboard</h2>\r\n      <div><b>Total Visits:</b> {stats.totalVisits}</div>\r\n      <h3>Visitor Log</h3>\r\n      <ul>\r\n        {stats.visits.map((v, i) => (\r\n          <li key={i}>{v.timestamp} — {v.ip} — {v.section}</li>\r\n        ))}\r\n      </ul>\r\n      <h3>Section Stats</h3>\r\n      <ul>\r\n        {stats.sectionStats.map((s, i) => (\r\n          <li key={i}>{s.section}: {s.count} views ({s.percent}%)</li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGN,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACd,MAAMU,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVF,QAAQ,CAAC,gCAAgC,CAAC;QAC1C;MACF;MACA,IAAI;QACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;UACxEC,OAAO,EAAE;YAAE,eAAe,EAAE,UAAUL,KAAK;UAAG;QAChD,CAAC,CAAC;QACF,MAAMM,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,IAAIJ,QAAQ,CAACK,EAAE,EAAE;UACfZ,QAAQ,CAACU,IAAI,CAAC;QAChB,CAAC,MAAM;UACLR,QAAQ,CAACQ,IAAI,CAACG,OAAO,IAAI,uBAAuB,CAAC;QACnD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZZ,QAAQ,CAAC,eAAe,CAAC;MAC3B;IACF,CAAC;IACDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,KAAK,EAAE,oBAAOL,OAAA;IAAKmB,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE;IAAG,CAAE;IAAAC,QAAA,EAAEjB;EAAK;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzE,IAAI,CAACvB,KAAK,EAAE,oBAAOH,OAAA;IAAKmB,KAAK,EAAE;MAAEE,MAAM,EAAE;IAAG,CAAE;IAAAC,QAAA,EAAC;EAAoB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAEzE,oBACE1B,OAAA;IAAKmB,KAAK,EAAE;MAAEE,MAAM,EAAE;IAAG,CAAE;IAAAC,QAAA,gBACzBtB,OAAA;MAAAsB,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxB1B,OAAA;MAAAsB,QAAA,gBAAKtB,OAAA;QAAAsB,QAAA,EAAG;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,KAAC,EAACvB,KAAK,CAACwB,WAAW;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnD1B,OAAA;MAAAsB,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpB1B,OAAA;MAAAsB,QAAA,EACGnB,KAAK,CAACyB,MAAM,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACrB/B,OAAA;QAAAsB,QAAA,GAAaQ,CAAC,CAACE,SAAS,EAAC,UAAG,EAACF,CAAC,CAACG,EAAE,EAAC,UAAG,EAACH,CAAC,CAACI,OAAO;MAAA,GAAtCH,CAAC;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA0C,CACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eACL1B,OAAA;MAAAsB,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtB1B,OAAA;MAAAsB,QAAA,EACGnB,KAAK,CAACgC,YAAY,CAACN,GAAG,CAAC,CAACO,CAAC,EAAEL,CAAC,kBAC3B/B,OAAA;QAAAsB,QAAA,GAAac,CAAC,CAACF,OAAO,EAAC,IAAE,EAACE,CAAC,CAACC,KAAK,EAAC,UAAQ,EAACD,CAAC,CAACE,OAAO,EAAC,IAAE;MAAA,GAA9CP,CAAC;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiD,CAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACxB,EAAA,CAjDID,cAAc;AAAAsC,EAAA,GAAdtC,cAAc;AAmDpB,eAAeA,cAAc;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}