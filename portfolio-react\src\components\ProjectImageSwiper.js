import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

const ProjectImageSwiper = ({ images, title, isNDA = false }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [loginMessage, setLoginMessage] = useState('');

  // Handle escape key to close fullscreen
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isFullscreen]);

  const openFullscreen = (imageIndex) => {
    setCurrentImageIndex(imageIndex);
    setIsFullscreen(true);
  };

  const closeFullscreen = () => {
    setIsFullscreen(false);
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      closeFullscreen();
    }
  };

  // --- Login Handler ---
  const handleLogin = async (e) => {
    e.preventDefault();
    setLoginMessage('');
    try {
      const response = await fetch('http://localhost:5000/api/admin/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: loginEmail, password: loginPassword })
      });
      const data = await response.json();
      if (data.token) {
        localStorage.setItem('token', data.token);
        setLoginMessage('Login successful!');
      } else {
        setLoginMessage(data.message || 'Login failed');
      }
    } catch (err) {
      setLoginMessage('Network error');
    }
  };

  return (
    <>
      {/* --- Admin Login Form --- */}
      <div style={{ margin: '2rem 0', padding: '1rem', border: '1px solid #ccc', maxWidth: 400 }}>
        <h3>Admin Login</h3>
        <form onSubmit={handleLogin} autoComplete="off">
          <input
            name="email"
            type="email"
            placeholder="Email"
            value={loginEmail}
            onChange={e => setLoginEmail(e.target.value)}
            required
            style={{ display: 'block', marginBottom: 8, width: '100%' }}
          />
          <input
            name="password"
            type="password"
            placeholder="Password"
            value={loginPassword}
            onChange={e => setLoginPassword(e.target.value)}
            required
            style={{ display: 'block', marginBottom: 8, width: '100%' }}
          />
          <button type="submit" style={{ width: '100%' }}>Login</button>
        </form>
        {loginMessage && <div style={{ marginTop: 8, color: loginMessage.includes('success') ? 'green' : 'red' }}>{loginMessage}</div>}
      </div>

      {/* --- Project Image Swiper UI --- */}
      <div className="project-image-swiper">
      <Swiper
        modules={[Navigation, Pagination, Autoplay, EffectFade]}
        spaceBetween={0}
        slidesPerView={1}
        navigation={{
          nextEl: '.swiper-button-next-custom',
          prevEl: '.swiper-button-prev-custom',
        }}
        pagination={{
          clickable: true,
          dynamicBullets: true,
        }}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        effect="fade"
        fadeEffect={{
          crossFade: true
        }}
        loop={images.length > 1}
        className="project-swiper"
      >
        {images.map((image, index) => (
          <SwiperSlide key={index}>
            <div className="swiper-slide-content">
              <img
                src={image}
                alt={`${title} - View ${index + 1}`}
                className="swiper-image"
              />
              {/* Fullscreen Icon */}
              <div
                className="fullscreen-icon"
                onClick={(e) => {
                  e.stopPropagation();
                  openFullscreen(index);
                }}
                title="View Fullscreen"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 14H5V19H10V17H7V14Z" fill="currentColor"/>
                  <path d="M5 10H7V7H10V5H5V10Z" fill="currentColor"/>
                  <path d="M17 14H19V19H14V17H17V14Z" fill="currentColor"/>
                  <path d="M14 5V7H17V10H19V5H14Z" fill="currentColor"/>
                </svg>
              </div>
            </div>
          </SwiperSlide>
        ))}
        {/* Custom Navigation Buttons */}
        {images.length > 1 && (
          <>
            <div className="swiper-button-prev-custom">
              <span>‹</span>
            </div>
            <div className="swiper-button-next-custom">
              <span>›</span>
            </div>
          </>
        )}
        {/* Swipe Indicator */}
        {images.length > 1 && (
          <div className="swipe-indicator">
            <span className="swipe-text">Swipe</span>
            <div className="swipe-animation">
              <div className="swipe-dot"></div>
              <div className="swipe-dot"></div>
              <div className="swipe-dot"></div>
            </div>
          </div>
        )}
      </Swiper>
      </div>

      {/* Fullscreen Modal - Rendered as Portal */}
      {isFullscreen && createPortal(
        <div className="fullscreen-modal" onClick={handleBackdropClick}>
          <div className="fullscreen-content">
            <button
              className="fullscreen-close"
              onClick={closeFullscreen}
              title="Close (Esc)"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <img
              src={images[currentImageIndex]}
              alt={`${title} - Fullscreen View`}
              className="fullscreen-image"
            />
            {images.length > 1 && (
              <div className="fullscreen-navigation">
                <button
                  className="fullscreen-nav-btn fullscreen-prev"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentImageIndex((prev) =>
                      prev === 0 ? images.length - 1 : prev - 1
                    );
                  }}
                  title="Previous Image"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <button
                  className="fullscreen-nav-btn fullscreen-next"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentImageIndex((prev) =>
                      prev === images.length - 1 ? 0 : prev + 1
                    );
                  }}
                  title="Next Image"
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            )}
            <div className="fullscreen-counter">
              {currentImageIndex + 1} / {images.length}
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
};

export default ProjectImageSwiper;
