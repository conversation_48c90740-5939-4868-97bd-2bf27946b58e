{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\ProjectImageSwiper.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProjectImageSwiper = ({\n  images,\n  title,\n  isNDA = false\n}) => {\n  _s();\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [loginEmail, setLoginEmail] = useState('');\n  const [loginPassword, setLoginPassword] = useState('');\n  const [loginMessage, setLoginMessage] = useState('');\n\n  // Handle escape key to close fullscreen\n  useEffect(() => {\n    const handleEscapeKey = event => {\n      if (event.key === 'Escape' && isFullscreen) {\n        setIsFullscreen(false);\n      }\n    };\n    if (isFullscreen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isFullscreen]);\n  const openFullscreen = imageIndex => {\n    setCurrentImageIndex(imageIndex);\n    setIsFullscreen(true);\n  };\n  const closeFullscreen = () => {\n    setIsFullscreen(false);\n  };\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      closeFullscreen();\n    }\n  };\n\n  // --- Login Handler ---\n  const handleLogin = async e => {\n    e.preventDefault(); // Prevents the default form submission\n    setLoginMessage('');\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email: loginEmail,\n          password: loginPassword\n        })\n      });\n      const data = await response.json();\n      if (data.token) {\n        localStorage.setItem('token', data.token);\n        setLoginMessage('Login successful!');\n        // Optionally redirect to dashboard here\n      } else {\n        setLoginMessage(data.message || 'Login failed');\n      }\n    } catch (err) {\n      setLoginMessage('Network error');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        margin: '2rem 0',\n        padding: '1rem',\n        border: '1px solid #ccc',\n        maxWidth: 400\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Admin Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleLogin,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          name: \"email\",\n          type: \"email\",\n          placeholder: \"Email\",\n          value: loginEmail,\n          onChange: e => setLoginEmail(e.target.value),\n          required: true,\n          style: {\n            display: 'block',\n            marginBottom: 8,\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          name: \"password\",\n          type: \"password\",\n          placeholder: \"Password\",\n          value: loginPassword,\n          onChange: e => setLoginPassword(e.target.value),\n          required: true,\n          style: {\n            display: 'block',\n            marginBottom: 8,\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          style: {\n            width: '100%'\n          },\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), loginMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 8,\n          color: loginMessage.includes('success') ? 'green' : 'red'\n        },\n        children: loginMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 26\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"project-image-swiper\",\n      children: /*#__PURE__*/_jsxDEV(Swiper, {\n        modules: [Navigation, Pagination, Autoplay, EffectFade],\n        spaceBetween: 0,\n        slidesPerView: 1,\n        navigation: {\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom'\n        },\n        pagination: {\n          clickable: true,\n          dynamicBullets: true\n        },\n        autoplay: {\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true\n        },\n        effect: \"fade\",\n        fadeEffect: {\n          crossFade: true\n        },\n        loop: images.length > 1,\n        className: \"project-swiper\",\n        children: [images.map((image, index) => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-slide-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: image,\n              alt: `${title} - View ${index + 1}`,\n              className: \"swiper-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fullscreen-icon\",\n              onClick: e => {\n                e.stopPropagation();\n                openFullscreen(index);\n              },\n              title: \"View Fullscreen\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M7 14H5V19H10V17H7V14Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M5 10H7V7H10V5H5V10Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M17 14H19V19H14V17H17V14Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M14 5V7H17V10H19V5H14Z\",\n                  fill: \"currentColor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)), images.length > 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-button-prev-custom\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2039\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-button-next-custom\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u203A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"swipe-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"swipe-text\",\n            children: \"Swipe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swipe-animation\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), isFullscreen && /*#__PURE__*/createPortal(/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fullscreen-modal\",\n      onClick: handleBackdropClick,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fullscreen-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"fullscreen-close\",\n          onClick: closeFullscreen,\n          title: \"Close (Esc)\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M18 6L6 18M6 6L18 18\",\n              stroke: \"currentColor\",\n              strokeWidth: \"2\",\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: images[currentImageIndex],\n          alt: `${title} - Fullscreen View`,\n          className: \"fullscreen-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fullscreen-navigation\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"fullscreen-nav-btn fullscreen-prev\",\n            onClick: e => {\n              e.stopPropagation();\n              setCurrentImageIndex(prev => prev === 0 ? images.length - 1 : prev - 1);\n            },\n            title: \"Previous Image\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M15 18L9 12L15 6\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"fullscreen-nav-btn fullscreen-next\",\n            onClick: e => {\n              e.stopPropagation();\n              setCurrentImageIndex(prev => prev === images.length - 1 ? 0 : prev + 1);\n            },\n            title: \"Next Image\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M9 18L15 12L9 6\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fullscreen-counter\",\n          children: [currentImageIndex + 1, \" / \", images.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this), document.body)]\n  }, void 0, true);\n};\n_s(ProjectImageSwiper, \"oYXLIXVO3a8swoKYVYuL1lmdz9I=\");\n_c = ProjectImageSwiper;\nexport default ProjectImageSwiper;\nvar _c;\n$RefreshReg$(_c, \"ProjectImageSwiper\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createPortal", "Swiper", "SwiperSlide", "Navigation", "Pagination", "Autoplay", "EffectFade", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProjectImageSwiper", "images", "title", "isNDA", "_s", "isFullscreen", "setIsFullscreen", "currentImageIndex", "setCurrentImageIndex", "loginEmail", "setLoginEmail", "loginPassword", "setLoginPassword", "loginMessage", "setLoginMessage", "handleEscapeKey", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "openFullscreen", "imageIndex", "closeFullscreen", "handleBackdropClick", "e", "target", "currentTarget", "handleLogin", "preventDefault", "response", "fetch", "method", "headers", "JSON", "stringify", "email", "password", "data", "json", "token", "localStorage", "setItem", "message", "err", "children", "margin", "padding", "border", "max<PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "autoComplete", "name", "type", "placeholder", "value", "onChange", "required", "display", "marginBottom", "width", "marginTop", "color", "includes", "className", "modules", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "navigation", "nextEl", "prevEl", "pagination", "clickable", "dynamicBullets", "autoplay", "delay", "disableOnInteraction", "pauseOnMouseEnter", "effect", "fadeEffect", "crossFade", "loop", "length", "map", "image", "index", "src", "alt", "onClick", "stopPropagation", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "prev", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/ProjectImageSwiper.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\n\nconst ProjectImageSwiper = ({ images, title, isNDA = false }) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [loginEmail, setLoginEmail] = useState('');\n  const [loginPassword, setLoginPassword] = useState('');\n  const [loginMessage, setLoginMessage] = useState('');\n\n  // Handle escape key to close fullscreen\n  useEffect(() => {\n    const handleEscapeKey = (event) => {\n      if (event.key === 'Escape' && isFullscreen) {\n        setIsFullscreen(false);\n      }\n    };\n\n    if (isFullscreen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isFullscreen]);\n\n  const openFullscreen = (imageIndex) => {\n    setCurrentImageIndex(imageIndex);\n    setIsFullscreen(true);\n  };\n\n  const closeFullscreen = () => {\n    setIsFullscreen(false);\n  };\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      closeFullscreen();\n    }\n  };\n\n  // --- Login Handler ---\n  const handleLogin = async (e) => {\n    e.preventDefault(); // Prevents the default form submission\n    setLoginMessage('');\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/login', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email: loginEmail, password: loginPassword })\n      });\n      const data = await response.json();\n      if (data.token) {\n        localStorage.setItem('token', data.token);\n        setLoginMessage('Login successful!');\n        // Optionally redirect to dashboard here\n      } else {\n        setLoginMessage(data.message || 'Login failed');\n      }\n    } catch (err) {\n      setLoginMessage('Network error');\n    }\n  };\n\n  return (\n    <>\n      {/* --- Admin Login Form --- */}\n      <div style={{ margin: '2rem 0', padding: '1rem', border: '1px solid #ccc', maxWidth: 400 }}>\n        <h3>Admin Login</h3>\n        <form onSubmit={handleLogin} autoComplete=\"off\">\n          <input\n            name=\"email\"\n            type=\"email\"\n            placeholder=\"Email\"\n            value={loginEmail}\n            onChange={e => setLoginEmail(e.target.value)}\n            required\n            style={{ display: 'block', marginBottom: 8, width: '100%' }}\n          />\n          <input\n            name=\"password\"\n            type=\"password\"\n            placeholder=\"Password\"\n            value={loginPassword}\n            onChange={e => setLoginPassword(e.target.value)}\n            required\n            style={{ display: 'block', marginBottom: 8, width: '100%' }}\n          />\n          <button type=\"submit\" style={{ width: '100%' }}>Login</button>\n        </form>\n        {loginMessage && <div style={{ marginTop: 8, color: loginMessage.includes('success') ? 'green' : 'red' }}>{loginMessage}</div>}\n      </div>\n\n      {/* --- Project Image Swiper UI --- */}\n      <div className=\"project-image-swiper\">\n      <Swiper\n        modules={[Navigation, Pagination, Autoplay, EffectFade]}\n        spaceBetween={0}\n        slidesPerView={1}\n        navigation={{\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom',\n        }}\n        pagination={{\n          clickable: true,\n          dynamicBullets: true,\n        }}\n        autoplay={{\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true,\n        }}\n        effect=\"fade\"\n        fadeEffect={{\n          crossFade: true\n        }}\n        loop={images.length > 1}\n        className=\"project-swiper\"\n      >\n        {images.map((image, index) => (\n          <SwiperSlide key={index}>\n            <div className=\"swiper-slide-content\">\n              <img\n                src={image}\n                alt={`${title} - View ${index + 1}`}\n                className=\"swiper-image\"\n              />\n              {/* Fullscreen Icon */}\n              <div\n                className=\"fullscreen-icon\"\n                onClick={(e) => {\n                  e.stopPropagation();\n                  openFullscreen(index);\n                }}\n                title=\"View Fullscreen\"\n              >\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M7 14H5V19H10V17H7V14Z\" fill=\"currentColor\"/>\n                  <path d=\"M5 10H7V7H10V5H5V10Z\" fill=\"currentColor\"/>\n                  <path d=\"M17 14H19V19H14V17H17V14Z\" fill=\"currentColor\"/>\n                  <path d=\"M14 5V7H17V10H19V5H14Z\" fill=\"currentColor\"/>\n                </svg>\n              </div>\n            </div>\n          </SwiperSlide>\n        ))}\n        {/* Custom Navigation Buttons */}\n        {images.length > 1 && (\n          <>\n            <div className=\"swiper-button-prev-custom\">\n              <span>‹</span>\n            </div>\n            <div className=\"swiper-button-next-custom\">\n              <span>›</span>\n            </div>\n          </>\n        )}\n        {/* Swipe Indicator */}\n        {images.length > 1 && (\n          <div className=\"swipe-indicator\">\n            <span className=\"swipe-text\">Swipe</span>\n            <div className=\"swipe-animation\">\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n            </div>\n          </div>\n        )}\n      </Swiper>\n      </div>\n\n      {/* Fullscreen Modal - Rendered as Portal */}\n      {isFullscreen && createPortal(\n        <div className=\"fullscreen-modal\" onClick={handleBackdropClick}>\n          <div className=\"fullscreen-content\">\n            <button\n              className=\"fullscreen-close\"\n              onClick={closeFullscreen}\n              title=\"Close (Esc)\"\n            >\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n              </svg>\n            </button>\n            <img\n              src={images[currentImageIndex]}\n              alt={`${title} - Fullscreen View`}\n              className=\"fullscreen-image\"\n            />\n            {images.length > 1 && (\n              <div className=\"fullscreen-navigation\">\n                <button\n                  className=\"fullscreen-nav-btn fullscreen-prev\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setCurrentImageIndex((prev) =>\n                      prev === 0 ? images.length - 1 : prev - 1\n                    );\n                  }}\n                  title=\"Previous Image\"\n                >\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M15 18L9 12L15 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                </button>\n                <button\n                  className=\"fullscreen-nav-btn fullscreen-next\"\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setCurrentImageIndex((prev) =>\n                      prev === images.length - 1 ? 0 : prev + 1\n                    );\n                  }}\n                  title=\"Next Image\"\n                >\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M9 18L15 12L9 6\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                </button>\n              </div>\n            )}\n            <div className=\"fullscreen-counter\">\n              {currentImageIndex + 1} / {images.length}\n            </div>\n          </div>\n        </div>,\n        document.body\n      )}\n    </>\n  );\n};\n\nexport default ProjectImageSwiper;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,QAAQ,WAAW;AACxC,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;;AAE7E;AACA,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAEC,KAAK,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2B,eAAe,GAAIC,KAAK,IAAK;MACjC,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAIZ,YAAY,EAAE;QAC1CC,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAED,IAAID,YAAY,EAAE;MAChBa,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,eAAe,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,eAAe,CAAC;MACxDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACjB,YAAY,CAAC,CAAC;EAElB,MAAMmB,cAAc,GAAIC,UAAU,IAAK;IACrCjB,oBAAoB,CAACiB,UAAU,CAAC;IAChCnB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMoB,eAAe,GAAGA,CAAA,KAAM;IAC5BpB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMqB,mBAAmB,GAAIC,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCJ,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMK,WAAW,GAAG,MAAOH,CAAC,IAAK;IAC/BA,CAAC,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC;IACpBlB,eAAe,CAAC,EAAE,CAAC;IACnB,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/ChB,IAAI,EAAEiB,IAAI,CAACC,SAAS,CAAC;UAAEC,KAAK,EAAE9B,UAAU;UAAE+B,QAAQ,EAAE7B;QAAc,CAAC;MACrE,CAAC,CAAC;MACF,MAAM8B,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,KAAK,EAAE;QACdC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,IAAI,CAACE,KAAK,CAAC;QACzC7B,eAAe,CAAC,mBAAmB,CAAC;QACpC;MACF,CAAC,MAAM;QACLA,eAAe,CAAC2B,IAAI,CAACK,OAAO,IAAI,cAAc,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZjC,eAAe,CAAC,eAAe,CAAC;IAClC;EACF,CAAC;EAED,oBACEjB,OAAA,CAAAE,SAAA;IAAAiD,QAAA,gBAEEnD,OAAA;MAAKwB,KAAK,EAAE;QAAE4B,MAAM,EAAE,QAAQ;QAAEC,OAAO,EAAE,MAAM;QAAEC,MAAM,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAAAJ,QAAA,gBACzFnD,OAAA;QAAAmD,QAAA,EAAI;MAAW;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpB3D,OAAA;QAAM4D,QAAQ,EAAE1B,WAAY;QAAC2B,YAAY,EAAC,KAAK;QAAAV,QAAA,gBAC7CnD,OAAA;UACE8D,IAAI,EAAC,OAAO;UACZC,IAAI,EAAC,OAAO;UACZC,WAAW,EAAC,OAAO;UACnBC,KAAK,EAAErD,UAAW;UAClBsD,QAAQ,EAAEnC,CAAC,IAAIlB,aAAa,CAACkB,CAAC,CAACC,MAAM,CAACiC,KAAK,CAAE;UAC7CE,QAAQ;UACR3C,KAAK,EAAE;YAAE4C,OAAO,EAAE,OAAO;YAAEC,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAO;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF3D,OAAA;UACE8D,IAAI,EAAC,UAAU;UACfC,IAAI,EAAC,UAAU;UACfC,WAAW,EAAC,UAAU;UACtBC,KAAK,EAAEnD,aAAc;UACrBoD,QAAQ,EAAEnC,CAAC,IAAIhB,gBAAgB,CAACgB,CAAC,CAACC,MAAM,CAACiC,KAAK,CAAE;UAChDE,QAAQ;UACR3C,KAAK,EAAE;YAAE4C,OAAO,EAAE,OAAO;YAAEC,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAO;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACF3D,OAAA;UAAQ+D,IAAI,EAAC,QAAQ;UAACvC,KAAK,EAAE;YAAE8C,KAAK,EAAE;UAAO,CAAE;UAAAnB,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EACN3C,YAAY,iBAAIhB,OAAA;QAAKwB,KAAK,EAAE;UAAE+C,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAExD,YAAY,CAACyD,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,GAAG;QAAM,CAAE;QAAAtB,QAAA,EAAEnC;MAAY;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3H,CAAC,eAGN3D,OAAA;MAAK0E,SAAS,EAAC,sBAAsB;MAAAvB,QAAA,eACrCnD,OAAA,CAACP,MAAM;QACLkF,OAAO,EAAE,CAAChF,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,CAAE;QACxD8E,YAAY,EAAE,CAAE;QAChBC,aAAa,EAAE,CAAE;QACjBC,UAAU,EAAE;UACVC,MAAM,EAAE,4BAA4B;UACpCC,MAAM,EAAE;QACV,CAAE;QACFC,UAAU,EAAE;UACVC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAE;QACFC,QAAQ,EAAE;UACRC,KAAK,EAAE,IAAI;UACXC,oBAAoB,EAAE,KAAK;UAC3BC,iBAAiB,EAAE;QACrB,CAAE;QACFC,MAAM,EAAC,MAAM;QACbC,UAAU,EAAE;UACVC,SAAS,EAAE;QACb,CAAE;QACFC,IAAI,EAAEvF,MAAM,CAACwF,MAAM,GAAG,CAAE;QACxBlB,SAAS,EAAC,gBAAgB;QAAAvB,QAAA,GAEzB/C,MAAM,CAACyF,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvB/F,OAAA,CAACN,WAAW;UAAAyD,QAAA,eACVnD,OAAA;YAAK0E,SAAS,EAAC,sBAAsB;YAAAvB,QAAA,gBACnCnD,OAAA;cACEgG,GAAG,EAAEF,KAAM;cACXG,GAAG,EAAE,GAAG5F,KAAK,WAAW0F,KAAK,GAAG,CAAC,EAAG;cACpCrB,SAAS,EAAC;YAAc;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eAEF3D,OAAA;cACE0E,SAAS,EAAC,iBAAiB;cAC3BwB,OAAO,EAAGnE,CAAC,IAAK;gBACdA,CAAC,CAACoE,eAAe,CAAC,CAAC;gBACnBxE,cAAc,CAACoE,KAAK,CAAC;cACvB,CAAE;cACF1F,KAAK,EAAC,iBAAiB;cAAA8C,QAAA,eAEvBnD,OAAA;gBAAKsE,KAAK,EAAC,IAAI;gBAAC8B,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,4BAA4B;gBAAApD,QAAA,gBAC5FnD,OAAA;kBAAMwG,CAAC,EAAC,wBAAwB;kBAACF,IAAI,EAAC;gBAAc;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACtD3D,OAAA;kBAAMwG,CAAC,EAAC,sBAAsB;kBAACF,IAAI,EAAC;gBAAc;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACpD3D,OAAA;kBAAMwG,CAAC,EAAC,2BAA2B;kBAACF,IAAI,EAAC;gBAAc;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACzD3D,OAAA;kBAAMwG,CAAC,EAAC,wBAAwB;kBAACF,IAAI,EAAC;gBAAc;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAvBUoC,KAAK;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBV,CACd,CAAC,EAEDvD,MAAM,CAACwF,MAAM,GAAG,CAAC,iBAChB5F,OAAA,CAAAE,SAAA;UAAAiD,QAAA,gBACEnD,OAAA;YAAK0E,SAAS,EAAC,2BAA2B;YAAAvB,QAAA,eACxCnD,OAAA;cAAAmD,QAAA,EAAM;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACN3D,OAAA;YAAK0E,SAAS,EAAC,2BAA2B;YAAAvB,QAAA,eACxCnD,OAAA;cAAAmD,QAAA,EAAM;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA,eACN,CACH,EAEAvD,MAAM,CAACwF,MAAM,GAAG,CAAC,iBAChB5F,OAAA;UAAK0E,SAAS,EAAC,iBAAiB;UAAAvB,QAAA,gBAC9BnD,OAAA;YAAM0E,SAAS,EAAC,YAAY;YAAAvB,QAAA,EAAC;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzC3D,OAAA;YAAK0E,SAAS,EAAC,iBAAiB;YAAAvB,QAAA,gBAC9BnD,OAAA;cAAK0E,SAAS,EAAC;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjC3D,OAAA;cAAK0E,SAAS,EAAC;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjC3D,OAAA;cAAK0E,SAAS,EAAC;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLnD,YAAY,iBAAIhB,YAAY,cAC3BQ,OAAA;MAAK0E,SAAS,EAAC,kBAAkB;MAACwB,OAAO,EAAEpE,mBAAoB;MAAAqB,QAAA,eAC7DnD,OAAA;QAAK0E,SAAS,EAAC,oBAAoB;QAAAvB,QAAA,gBACjCnD,OAAA;UACE0E,SAAS,EAAC,kBAAkB;UAC5BwB,OAAO,EAAErE,eAAgB;UACzBxB,KAAK,EAAC,aAAa;UAAA8C,QAAA,eAEnBnD,OAAA;YAAKsE,KAAK,EAAC,IAAI;YAAC8B,MAAM,EAAC,IAAI;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,MAAM;YAACC,KAAK,EAAC,4BAA4B;YAAApD,QAAA,eAC5FnD,OAAA;cAAMwG,CAAC,EAAC,sBAAsB;cAACC,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC,GAAG;cAACC,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC;YAAO;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACT3D,OAAA;UACEgG,GAAG,EAAE5F,MAAM,CAACM,iBAAiB,CAAE;UAC/BuF,GAAG,EAAE,GAAG5F,KAAK,oBAAqB;UAClCqE,SAAS,EAAC;QAAkB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,EACDvD,MAAM,CAACwF,MAAM,GAAG,CAAC,iBAChB5F,OAAA;UAAK0E,SAAS,EAAC,uBAAuB;UAAAvB,QAAA,gBACpCnD,OAAA;YACE0E,SAAS,EAAC,oCAAoC;YAC9CwB,OAAO,EAAGnE,CAAC,IAAK;cACdA,CAAC,CAACoE,eAAe,CAAC,CAAC;cACnBxF,oBAAoB,CAAEkG,IAAI,IACxBA,IAAI,KAAK,CAAC,GAAGzG,MAAM,CAACwF,MAAM,GAAG,CAAC,GAAGiB,IAAI,GAAG,CAC1C,CAAC;YACH,CAAE;YACFxG,KAAK,EAAC,gBAAgB;YAAA8C,QAAA,eAEtBnD,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAAC8B,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,KAAK,EAAC,4BAA4B;cAAApD,QAAA,eAC5FnD,OAAA;gBAAMwG,CAAC,EAAC,kBAAkB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC;cAAO;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT3D,OAAA;YACE0E,SAAS,EAAC,oCAAoC;YAC9CwB,OAAO,EAAGnE,CAAC,IAAK;cACdA,CAAC,CAACoE,eAAe,CAAC,CAAC;cACnBxF,oBAAoB,CAAEkG,IAAI,IACxBA,IAAI,KAAKzG,MAAM,CAACwF,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGiB,IAAI,GAAG,CAC1C,CAAC;YACH,CAAE;YACFxG,KAAK,EAAC,YAAY;YAAA8C,QAAA,eAElBnD,OAAA;cAAKsE,KAAK,EAAC,IAAI;cAAC8B,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAACC,KAAK,EAAC,4BAA4B;cAAApD,QAAA,eAC5FnD,OAAA;gBAAMwG,CAAC,EAAC,iBAAiB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC;cAAO;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eACD3D,OAAA;UAAK0E,SAAS,EAAC,oBAAoB;UAAAvB,QAAA,GAChCzC,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACN,MAAM,CAACwF,MAAM;QAAA;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACNtC,QAAQ,CAACE,IACX,CAAC;EAAA,eACD,CAAC;AAEP,CAAC;AAAChB,EAAA,CAxOIJ,kBAAkB;AAAA2G,EAAA,GAAlB3G,kBAAkB;AA0OxB,eAAeA,kBAAkB;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}