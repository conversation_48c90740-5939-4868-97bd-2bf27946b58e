{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\AdminLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminLogin = () => {\n  _s();\n  const [loginEmail, setLoginEmail] = useState('');\n  const [loginPassword, setLoginPassword] = useState('');\n  const [loginMessage, setLoginMessage] = useState('');\n  const navigate = useNavigate();\n  const handleLogin = async e => {\n    e.preventDefault();\n    setLoginMessage('');\n    console.log('Login handler called');\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email: loginEmail,\n          password: loginPassword\n        })\n      });\n      const data = await response.json();\n      if (data.token) {\n        localStorage.setItem('token', data.token);\n        setLoginMessage('Login successful! Redirecting...');\n        setTimeout(() => navigate('/admin/dashboard'), 1000);\n      } else {\n        setLoginMessage(data.message || 'Login failed');\n      }\n    } catch (err) {\n      setLoginMessage('Network error');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      margin: '2rem 0',\n      padding: '1rem',\n      border: '1px solid #ccc',\n      maxWidth: 400\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Admin Login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleLogin,\n      autoComplete: \"off\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"email\",\n        type: \"email\",\n        placeholder: \"Email\",\n        value: loginEmail,\n        onChange: e => setLoginEmail(e.target.value),\n        required: true,\n        style: {\n          display: 'block',\n          marginBottom: 8,\n          width: '100%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"password\",\n        type: \"password\",\n        placeholder: \"Password\",\n        value: loginPassword,\n        onChange: e => setLoginPassword(e.target.value),\n        required: true,\n        style: {\n          display: 'block',\n          marginBottom: 8,\n          width: '100%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        style: {\n          width: '100%'\n        },\n        children: \"Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), loginMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: 8,\n        color: loginMessage.includes('success') ? 'green' : 'red'\n      },\n      children: loginMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 24\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminLogin, \"kv+s8xt2hx6/OoEfm/VbQfb3dTM=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminLogin;\nexport default AdminLogin;\nvar _c;\n$RefreshReg$(_c, \"AdminLogin\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "AdminLogin", "_s", "loginEmail", "setLoginEmail", "loginPassword", "setLoginPassword", "loginMessage", "setLoginMessage", "navigate", "handleLogin", "e", "preventDefault", "console", "log", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "email", "password", "data", "json", "token", "localStorage", "setItem", "setTimeout", "message", "err", "style", "margin", "padding", "border", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "autoComplete", "name", "type", "placeholder", "value", "onChange", "target", "required", "display", "marginBottom", "width", "marginTop", "color", "includes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/AdminLogin.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst AdminLogin = () => {\r\n  const [loginEmail, setLoginEmail] = useState('');\r\n  const [loginPassword, setLoginPassword] = useState('');\r\n  const [loginMessage, setLoginMessage] = useState('');\r\n  const navigate = useNavigate();\r\n\r\n  const handleLogin = async (e) => {\r\n    e.preventDefault();\r\n    setLoginMessage('');\r\n    console.log('Login handler called');\r\n    try {\r\n      const response = await fetch('http://localhost:5000/api/admin/login', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ email: loginEmail, password: loginPassword })\r\n      });\r\n      const data = await response.json();\r\n      if (data.token) {\r\n        localStorage.setItem('token', data.token);\r\n        setLoginMessage('Login successful! Redirecting...');\r\n        setTimeout(() => navigate('/admin/dashboard'), 1000);\r\n      } else {\r\n        setLoginMessage(data.message || 'Login failed');\r\n      }\r\n    } catch (err) {\r\n      setLoginMessage('Network error');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ margin: '2rem 0', padding: '1rem', border: '1px solid #ccc', maxWidth: 400 }}>\r\n      <h3>Admin Login</h3>\r\n      <form onSubmit={handleLogin} autoComplete=\"off\">\r\n        <input\r\n          name=\"email\"\r\n          type=\"email\"\r\n          placeholder=\"Email\"\r\n          value={loginEmail}\r\n          onChange={e => setLoginEmail(e.target.value)}\r\n          required\r\n          style={{ display: 'block', marginBottom: 8, width: '100%' }}\r\n        />\r\n        <input\r\n          name=\"password\"\r\n          type=\"password\"\r\n          placeholder=\"Password\"\r\n          value={loginPassword}\r\n          onChange={e => setLoginPassword(e.target.value)}\r\n          required\r\n          style={{ display: 'block', marginBottom: 8, width: '100%' }}\r\n        />\r\n        <button type=\"submit\" style={{ width: '100%' }}>Login</button>\r\n      </form>\r\n      {loginMessage && <div style={{ marginTop: 8, color: loginMessage.includes('success') ? 'green' : 'red' }}>{loginMessage}</div>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminLogin; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBJ,eAAe,CAAC,EAAE,CAAC;IACnBK,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,KAAK,EAAEnB,UAAU;UAAEoB,QAAQ,EAAElB;QAAc,CAAC;MACrE,CAAC,CAAC;MACF,MAAMmB,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,KAAK,EAAE;QACdC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,IAAI,CAACE,KAAK,CAAC;QACzClB,eAAe,CAAC,kCAAkC,CAAC;QACnDqB,UAAU,CAAC,MAAMpB,QAAQ,CAAC,kBAAkB,CAAC,EAAE,IAAI,CAAC;MACtD,CAAC,MAAM;QACLD,eAAe,CAACgB,IAAI,CAACM,OAAO,IAAI,cAAc,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZvB,eAAe,CAAC,eAAe,CAAC;IAClC;EACF,CAAC;EAED,oBACER,OAAA;IAAKgC,KAAK,EAAE;MAAEC,MAAM,EAAE,QAAQ;MAAEC,OAAO,EAAE,MAAM;MAAEC,MAAM,EAAE,gBAAgB;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBACzFrC,OAAA;MAAAqC,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBzC,OAAA;MAAM0C,QAAQ,EAAEhC,WAAY;MAACiC,YAAY,EAAC,KAAK;MAAAN,QAAA,gBAC7CrC,OAAA;QACE4C,IAAI,EAAC,OAAO;QACZC,IAAI,EAAC,OAAO;QACZC,WAAW,EAAC,OAAO;QACnBC,KAAK,EAAE5C,UAAW;QAClB6C,QAAQ,EAAErC,CAAC,IAAIP,aAAa,CAACO,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;QAC7CG,QAAQ;QACRlB,KAAK,EAAE;UAAEmB,OAAO,EAAE,OAAO;UAAEC,YAAY,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO;MAAE;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACFzC,OAAA;QACE4C,IAAI,EAAC,UAAU;QACfC,IAAI,EAAC,UAAU;QACfC,WAAW,EAAC,UAAU;QACtBC,KAAK,EAAE1C,aAAc;QACrB2C,QAAQ,EAAErC,CAAC,IAAIL,gBAAgB,CAACK,CAAC,CAACsC,MAAM,CAACF,KAAK,CAAE;QAChDG,QAAQ;QACRlB,KAAK,EAAE;UAAEmB,OAAO,EAAE,OAAO;UAAEC,YAAY,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO;MAAE;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACFzC,OAAA;QAAQ6C,IAAI,EAAC,QAAQ;QAACb,KAAK,EAAE;UAAEqB,KAAK,EAAE;QAAO,CAAE;QAAAhB,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EACNlC,YAAY,iBAAIP,OAAA;MAAKgC,KAAK,EAAE;QAAEsB,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAEhD,YAAY,CAACiD,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,GAAG;MAAM,CAAE;MAAAnB,QAAA,EAAE9B;IAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3H,CAAC;AAEV,CAAC;AAACvC,EAAA,CAxDID,UAAU;EAAA,QAIGH,WAAW;AAAA;AAAA2D,EAAA,GAJxBxD,UAAU;AA0DhB,eAAeA,UAAU;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}