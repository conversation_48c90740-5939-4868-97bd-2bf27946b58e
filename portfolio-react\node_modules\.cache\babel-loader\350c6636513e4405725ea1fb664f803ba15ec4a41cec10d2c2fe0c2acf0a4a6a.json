{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Experience.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Experience = () => {\n  const handleCompanyLinkClick = e => {\n    // Prevent the parent Link from being triggered when clicking the company link\n    e.stopPropagation();\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"experience\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Professional Experience\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeline\",\n      children: jobsData.map((job, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timeline-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timeline-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/job/${job.slug}`,\n          className: \"timeline-content-link\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: job.logo,\n              alt: job.logoAlt,\n              className: \"company-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"job-title\",\n              children: job.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"company-name\",\n              children: job.company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 17\n            }, this), job.companyLink && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"company-link\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"company-link-span\",\n                onClick: handleCompanyLinkClick,\n                style: {\n                  color: '#4B0082',\n                  textDecoration: 'underline',\n                  cursor: 'pointer'\n                },\n                role: \"link\",\n                tabIndex: 0,\n                onKeyPress: e => {\n                  if (e.key === 'Enter') window.open(job.companyLink, '_blank');\n                },\n                children: job.companyLink\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-duration\",\n              children: job.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-description\",\n              children: job.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"view-details\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View Details \\u2192\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 13\n        }, this)]\n      }, job.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Experience;\nexport default Experience;\nvar _c;\n$RefreshReg$(_c, \"Experience\");", "map": {"version": 3, "names": ["React", "Link", "jobsData", "jsxDEV", "_jsxDEV", "Experience", "handleCompanyLinkClick", "e", "stopPropagation", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "job", "index", "to", "slug", "src", "logo", "alt", "logoAlt", "title", "company", "companyLink", "onClick", "style", "color", "textDecoration", "cursor", "role", "tabIndex", "onKeyPress", "key", "window", "open", "duration", "summary", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Experience.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\n\nconst Experience = () => {\n  const handleCompanyLinkClick = (e) => {\n    // Prevent the parent Link from being triggered when clicking the company link\n    e.stopPropagation();\n  };\n\n  return (\n    <section className=\"experience\">\n      <h2>Professional Experience</h2>\n      <div className=\"timeline\">\n        {jobsData.map((job, index) => (\n          <div key={job.id} className=\"timeline-item\">\n            <div className=\"timeline-dot\"></div>\n            <Link to={`/job/${job.slug}`} className=\"timeline-content-link\">\n              <div className=\"timeline-content\">\n                <img\n                  src={job.logo}\n                  alt={job.logoAlt}\n                  className=\"company-logo\"\n                />\n                <h3 className=\"job-title\">{job.title}</h3>\n                <h4 className=\"company-name\">{job.company}</h4>\n                {job.companyLink && (\n                  <p className=\"company-link\">\n                    <span\n                      className=\"company-link-span\"\n                      onClick={handleCompanyLinkClick}\n                      style={{ color: '#4B0082', textDecoration: 'underline', cursor: 'pointer' }}\n                      role=\"link\"\n                      tabIndex={0}\n                      onKeyPress={e => { if (e.key === 'Enter') window.open(job.companyLink, '_blank'); }}\n                    >\n                      {job.companyLink}\n                    </span>\n                  </p>\n                )}\n                <p className=\"job-duration\">{job.duration}</p>\n                <p className=\"job-description\">{job.summary}</p>\n                <div className=\"view-details\">\n                  <span>View Details →</span>\n                </div>\n              </div>\n            </Link>\n          </div>\n        ))}\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,MAAMC,sBAAsB,GAAIC,CAAC,IAAK;IACpC;IACAA,CAAC,CAACC,eAAe,CAAC,CAAC;EACrB,CAAC;EAED,oBACEJ,OAAA;IAASK,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAC7BN,OAAA;MAAAM,QAAA,EAAI;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChCV,OAAA;MAAKK,SAAS,EAAC,UAAU;MAAAC,QAAA,EACtBR,QAAQ,CAACa,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBb,OAAA;QAAkBK,SAAS,EAAC,eAAe;QAAAC,QAAA,gBACzCN,OAAA;UAAKK,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCV,OAAA,CAACH,IAAI;UAACiB,EAAE,EAAE,QAAQF,GAAG,CAACG,IAAI,EAAG;UAACV,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eAC7DN,OAAA;YAAKK,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BN,OAAA;cACEgB,GAAG,EAAEJ,GAAG,CAACK,IAAK;cACdC,GAAG,EAAEN,GAAG,CAACO,OAAQ;cACjBd,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFV,OAAA;cAAIK,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEM,GAAG,CAACQ;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1CV,OAAA;cAAIK,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEM,GAAG,CAACS;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC9CE,GAAG,CAACU,WAAW,iBACdtB,OAAA;cAAGK,SAAS,EAAC,cAAc;cAAAC,QAAA,eACzBN,OAAA;gBACEK,SAAS,EAAC,mBAAmB;gBAC7BkB,OAAO,EAAErB,sBAAuB;gBAChCsB,KAAK,EAAE;kBAAEC,KAAK,EAAE,SAAS;kBAAEC,cAAc,EAAE,WAAW;kBAAEC,MAAM,EAAE;gBAAU,CAAE;gBAC5EC,IAAI,EAAC,MAAM;gBACXC,QAAQ,EAAE,CAAE;gBACZC,UAAU,EAAE3B,CAAC,IAAI;kBAAE,IAAIA,CAAC,CAAC4B,GAAG,KAAK,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACrB,GAAG,CAACU,WAAW,EAAE,QAAQ,CAAC;gBAAE,CAAE;gBAAAhB,QAAA,EAEnFM,GAAG,CAACU;cAAW;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACJ,eACDV,OAAA;cAAGK,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEM,GAAG,CAACsB;YAAQ;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CV,OAAA;cAAGK,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAEM,GAAG,CAACuB;YAAO;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDV,OAAA;cAAKK,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BN,OAAA;gBAAAM,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,GA/BCE,GAAG,CAACwB,EAAE;QAAA7B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgCX,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC2B,EAAA,GAhDIpC,UAAU;AAkDhB,eAAeA,UAAU;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}