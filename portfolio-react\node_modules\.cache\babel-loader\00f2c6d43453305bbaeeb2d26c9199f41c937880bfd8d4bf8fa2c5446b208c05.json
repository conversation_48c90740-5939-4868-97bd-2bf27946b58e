{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LONG_PRESS_DURATION = 5000; // 5 seconds\n\nconst Header = () => {\n  _s();\n  const [showAdminModal, setShowAdminModal] = useState(false);\n  const [adminEmail, setAdminEmail] = useState(\"\");\n  const [adminPassword, setAdminPassword] = useState(\"\");\n  const [adminLoginMessage, setAdminLoginMessage] = useState(\"\");\n  const longPressTimer = useRef(null);\n  const longPressTriggered = useRef(false);\n  const navigate = useNavigate();\n\n  // Start long press timer\n  const startLongPress = e => {\n    e.preventDefault();\n    longPressTriggered.current = false;\n    longPressTimer.current = setTimeout(() => {\n      setShowAdminModal(true);\n      longPressTriggered.current = true;\n    }, LONG_PRESS_DURATION);\n  };\n\n  // Cancel long press timer\n  const cancelLongPress = e => {\n    clearTimeout(longPressTimer.current);\n    if (!longPressTriggered.current && e.type === 'click') {\n      // Only reload if not a long press\n      window.location.reload();\n    }\n  };\n\n  // --- Admin Modal Login Handler ---\n  const handleAdminLogin = async e => {\n    e.preventDefault();\n    setAdminLoginMessage(\"\");\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email: adminEmail,\n          password: adminPassword\n        })\n      });\n      const data = await response.json();\n      if (data.token) {\n        localStorage.setItem('token', data.token);\n        setAdminLoginMessage('Login successful! Redirecting...');\n        setTimeout(() => {\n          setShowAdminModal(false);\n          navigate('/admin/dashboard');\n        }, 1000);\n      } else {\n        setAdminLoginMessage(data.message || 'Login failed');\n      }\n    } catch (err) {\n      setAdminLoginMessage('Network error');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo\",\n      children: /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/\",\n        onMouseDown: startLongPress,\n        onMouseUp: cancelLongPress,\n        onMouseLeave: cancelLongPress,\n        onTouchStart: startLongPress,\n        onTouchEnd: cancelLongPress,\n        onClick: cancelLongPress,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/logo.PNG\",\n          alt: \"Logo\",\n          className: \"logo-img\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\",\n      className: \"cv-button\",\n      target: \"_blank\",\n      rel: \"noopener noreferrer\",\n      children: \"Get CV\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), showAdminModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-modal-overlay\",\n      onClick: () => setShowAdminModal(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"admin-modal-close\",\n          onClick: () => setShowAdminModal(false),\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"admin-modal-title\",\n          children: \"Admin Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"admin-modal-form\",\n          onSubmit: handleAdminLogin,\n          autoComplete: \"off\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"admin-email\",\n            children: \"Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"admin-email\",\n            name: \"email\",\n            autoComplete: \"username\",\n            required: true,\n            value: adminEmail,\n            onChange: e => setAdminEmail(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"admin-password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"admin-password\",\n            name: \"password\",\n            autoComplete: \"current-password\",\n            required: true,\n            value: adminPassword,\n            onChange: e => setAdminPassword(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"admin-modal-submit\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), adminLoginMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8,\n            color: adminLoginMessage.includes('success') ? 'green' : 'red'\n          },\n          children: adminLoginMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 35\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"AL6H4pzl4QZS/SkHPsBHDpYbPt4=\", false, function () {\n  return [useNavigate];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "useNavigate", "jsxDEV", "_jsxDEV", "LONG_PRESS_DURATION", "Header", "_s", "showAdminModal", "setShowAdminModal", "adminEmail", "setAdminEmail", "adminPassword", "setAdminPassword", "adminLoginMessage", "setAdminLoginMessage", "longPressTimer", "longPressTriggered", "navigate", "startLongPress", "e", "preventDefault", "current", "setTimeout", "cancelLongPress", "clearTimeout", "type", "window", "location", "reload", "handleAdminLogin", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "email", "password", "data", "json", "token", "localStorage", "setItem", "message", "err", "children", "className", "href", "onMouseDown", "onMouseUp", "onMouseLeave", "onTouchStart", "onTouchEnd", "onClick", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "target", "rel", "stopPropagation", "onSubmit", "autoComplete", "htmlFor", "id", "name", "required", "value", "onChange", "style", "marginTop", "color", "includes", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/Header.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\n\nconst LONG_PRESS_DURATION = 5000; // 5 seconds\n\nconst Header = () => {\n  const [showAdminModal, setShowAdminModal] = useState(false);\n  const [adminEmail, setAdminEmail] = useState(\"\");\n  const [adminPassword, setAdminPassword] = useState(\"\");\n  const [adminLoginMessage, setAdminLoginMessage] = useState(\"\");\n  const longPressTimer = useRef(null);\n  const longPressTriggered = useRef(false);\n  const navigate = useNavigate();\n\n  // Start long press timer\n  const startLongPress = (e) => {\n    e.preventDefault();\n    longPressTriggered.current = false;\n    longPressTimer.current = setTimeout(() => {\n      setShowAdminModal(true);\n      longPressTriggered.current = true;\n    }, LONG_PRESS_DURATION);\n  };\n\n  // Cancel long press timer\n  const cancelLongPress = (e) => {\n    clearTimeout(longPressTimer.current);\n    if (!longPressTriggered.current && e.type === 'click') {\n      // Only reload if not a long press\n      window.location.reload();\n    }\n  };\n\n  // --- Admin Modal Login Handler ---\n  const handleAdminLogin = async (e) => {\n    e.preventDefault();\n    setAdminLoginMessage(\"\");\n    try {\n      const response = await fetch('http://localhost:5000/api/admin/login', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email: adminEmail, password: adminPassword })\n      });\n      const data = await response.json();\n      if (data.token) {\n        localStorage.setItem('token', data.token);\n        setAdminLoginMessage('Login successful! Redirecting...');\n        setTimeout(() => {\n          setShowAdminModal(false);\n          navigate('/admin/dashboard');\n        }, 1000);\n      } else {\n        setAdminLoginMessage(data.message || 'Login failed');\n      }\n    } catch (err) {\n      setAdminLoginMessage('Network error');\n    }\n  };\n\n  return (\n    <header>\n      <div className=\"logo\">\n        <a\n          href=\"/\"\n          onMouseDown={startLongPress}\n          onMouseUp={cancelLongPress}\n          onMouseLeave={cancelLongPress}\n          onTouchStart={startLongPress}\n          onTouchEnd={cancelLongPress}\n          onClick={cancelLongPress}\n        >\n          <img src=\"/logo.PNG\" alt=\"Logo\" className=\"logo-img\" />\n        </a>\n      </div>\n      <a \n        href=\"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93\" \n        className=\"cv-button\"\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n      >\n        Get CV\n      </a>\n      {showAdminModal && (\n        <div className=\"admin-modal-overlay\" onClick={() => setShowAdminModal(false)}>\n          <div className=\"admin-modal\" onClick={e => e.stopPropagation()}>\n            <button className=\"admin-modal-close\" onClick={() => setShowAdminModal(false)}>&times;</button>\n            <h2 className=\"admin-modal-title\">Admin Login</h2>\n            <form className=\"admin-modal-form\" onSubmit={handleAdminLogin} autoComplete=\"off\">\n              <label htmlFor=\"admin-email\">Email</label>\n              <input type=\"email\" id=\"admin-email\" name=\"email\" autoComplete=\"username\" required value={adminEmail} onChange={e => setAdminEmail(e.target.value)} />\n              <label htmlFor=\"admin-password\">Password</label>\n              <input type=\"password\" id=\"admin-password\" name=\"password\" autoComplete=\"current-password\" required value={adminPassword} onChange={e => setAdminPassword(e.target.value)} />\n              <button type=\"submit\" className=\"admin-modal-submit\">Login</button>\n            </form>\n            {adminLoginMessage && <div style={{ marginTop: 8, color: adminLoginMessage.includes('success') ? 'green' : 'red' }}>{adminLoginMessage}</div>}\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,mBAAmB,GAAG,IAAI,CAAC,CAAC;;AAElC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACe,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAMiB,cAAc,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMiB,kBAAkB,GAAGjB,MAAM,CAAC,KAAK,CAAC;EACxC,MAAMkB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMiB,cAAc,GAAIC,CAAC,IAAK;IAC5BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBJ,kBAAkB,CAACK,OAAO,GAAG,KAAK;IAClCN,cAAc,CAACM,OAAO,GAAGC,UAAU,CAAC,MAAM;MACxCd,iBAAiB,CAAC,IAAI,CAAC;MACvBQ,kBAAkB,CAACK,OAAO,GAAG,IAAI;IACnC,CAAC,EAAEjB,mBAAmB,CAAC;EACzB,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAIJ,CAAC,IAAK;IAC7BK,YAAY,CAACT,cAAc,CAACM,OAAO,CAAC;IACpC,IAAI,CAACL,kBAAkB,CAACK,OAAO,IAAIF,CAAC,CAACM,IAAI,KAAK,OAAO,EAAE;MACrD;MACAC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAOV,CAAC,IAAK;IACpCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBN,oBAAoB,CAAC,EAAE,CAAC;IACxB,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,KAAK,EAAE5B,UAAU;UAAE6B,QAAQ,EAAE3B;QAAc,CAAC;MACrE,CAAC,CAAC;MACF,MAAM4B,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,KAAK,EAAE;QACdC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,IAAI,CAACE,KAAK,CAAC;QACzC3B,oBAAoB,CAAC,kCAAkC,CAAC;QACxDQ,UAAU,CAAC,MAAM;UACfd,iBAAiB,CAAC,KAAK,CAAC;UACxBS,QAAQ,CAAC,kBAAkB,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLH,oBAAoB,CAACyB,IAAI,CAACK,OAAO,IAAI,cAAc,CAAC;MACtD;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ/B,oBAAoB,CAAC,eAAe,CAAC;IACvC;EACF,CAAC;EAED,oBACEX,OAAA;IAAA2C,QAAA,gBACE3C,OAAA;MAAK4C,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnB3C,OAAA;QACE6C,IAAI,EAAC,GAAG;QACRC,WAAW,EAAE/B,cAAe;QAC5BgC,SAAS,EAAE3B,eAAgB;QAC3B4B,YAAY,EAAE5B,eAAgB;QAC9B6B,YAAY,EAAElC,cAAe;QAC7BmC,UAAU,EAAE9B,eAAgB;QAC5B+B,OAAO,EAAE/B,eAAgB;QAAAuB,QAAA,eAEzB3C,OAAA;UAAKoD,GAAG,EAAC,WAAW;UAACC,GAAG,EAAC,MAAM;UAACT,SAAS,EAAC;QAAU;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACNzD,OAAA;MACE6C,IAAI,EAAC,iLAAiL;MACtLD,SAAS,EAAC,WAAW;MACrBc,MAAM,EAAC,QAAQ;MACfC,GAAG,EAAC,qBAAqB;MAAAhB,QAAA,EAC1B;IAED;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,EACHrD,cAAc,iBACbJ,OAAA;MAAK4C,SAAS,EAAC,qBAAqB;MAACO,OAAO,EAAEA,CAAA,KAAM9C,iBAAiB,CAAC,KAAK,CAAE;MAAAsC,QAAA,eAC3E3C,OAAA;QAAK4C,SAAS,EAAC,aAAa;QAACO,OAAO,EAAEnC,CAAC,IAAIA,CAAC,CAAC4C,eAAe,CAAC,CAAE;QAAAjB,QAAA,gBAC7D3C,OAAA;UAAQ4C,SAAS,EAAC,mBAAmB;UAACO,OAAO,EAAEA,CAAA,KAAM9C,iBAAiB,CAAC,KAAK,CAAE;UAAAsC,QAAA,EAAC;QAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC/FzD,OAAA;UAAI4C,SAAS,EAAC,mBAAmB;UAAAD,QAAA,EAAC;QAAW;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDzD,OAAA;UAAM4C,SAAS,EAAC,kBAAkB;UAACiB,QAAQ,EAAEnC,gBAAiB;UAACoC,YAAY,EAAC,KAAK;UAAAnB,QAAA,gBAC/E3C,OAAA;YAAO+D,OAAO,EAAC,aAAa;YAAApB,QAAA,EAAC;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CzD,OAAA;YAAOsB,IAAI,EAAC,OAAO;YAAC0C,EAAE,EAAC,aAAa;YAACC,IAAI,EAAC,OAAO;YAACH,YAAY,EAAC,UAAU;YAACI,QAAQ;YAACC,KAAK,EAAE7D,UAAW;YAAC8D,QAAQ,EAAEpD,CAAC,IAAIT,aAAa,CAACS,CAAC,CAAC0C,MAAM,CAACS,KAAK;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtJzD,OAAA;YAAO+D,OAAO,EAAC,gBAAgB;YAAApB,QAAA,EAAC;UAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChDzD,OAAA;YAAOsB,IAAI,EAAC,UAAU;YAAC0C,EAAE,EAAC,gBAAgB;YAACC,IAAI,EAAC,UAAU;YAACH,YAAY,EAAC,kBAAkB;YAACI,QAAQ;YAACC,KAAK,EAAE3D,aAAc;YAAC4D,QAAQ,EAAEpD,CAAC,IAAIP,gBAAgB,CAACO,CAAC,CAAC0C,MAAM,CAACS,KAAK;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7KzD,OAAA;YAAQsB,IAAI,EAAC,QAAQ;YAACsB,SAAS,EAAC,oBAAoB;YAAAD,QAAA,EAAC;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,EACN/C,iBAAiB,iBAAIV,OAAA;UAAKqE,KAAK,EAAE;YAAEC,SAAS,EAAE,CAAC;YAAEC,KAAK,EAAE7D,iBAAiB,CAAC8D,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,GAAG;UAAM,CAAE;UAAA7B,QAAA,EAAEjC;QAAiB;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1I;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEb,CAAC;AAACtD,EAAA,CA/FID,MAAM;EAAA,QAOOJ,WAAW;AAAA;AAAA2E,EAAA,GAPxBvE,MAAM;AAiGZ,eAAeA,MAAM;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}