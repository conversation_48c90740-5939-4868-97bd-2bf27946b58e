{"ast": null, "code": "export * from \"./iconsManifest.mjs\";\nexport * from \"./iconBase.mjs\";\nexport * from \"./iconContext.mjs\";", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/Desktop/Portfolio/node_modules/react-icons/lib/index.mjs"], "sourcesContent": ["export * from \"./iconsManifest.mjs\";\nexport * from \"./iconBase.mjs\";\nexport * from \"./iconContext.mjs\";"], "mappings": "AAAA,cAAc,qBAAqB;AACnC,cAAc,gBAAgB;AAC9B,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}