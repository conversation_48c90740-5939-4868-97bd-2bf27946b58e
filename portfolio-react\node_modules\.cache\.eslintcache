[{"C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\JobDetail.js": "4", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Home.js": "5", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Header.js": "6", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Footer.js": "7", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ProjectImageSwiper.js": "8", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\NDANotification.js": "9", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\IntroCrafting.js": "10", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Statistics.js": "11", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\SkillsTicker.js": "12", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Experience.js": "13", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Portfolio.js": "14", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ClientThoughts.js": "15", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Contact.js": "16", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Intro.js": "17", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\data\\jobsData.js": "18", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ScrollToTop.js": "19", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\AdminLogin.js": "20", "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\AdminDashboard.js": "21"}, {"size": 535, "mtime": 1750766591000, "results": "22", "hashOfConfig": "23"}, {"size": 724, "mtime": 1751032873047, "results": "24", "hashOfConfig": "23"}, {"size": 362, "mtime": 1750766591000, "results": "25", "hashOfConfig": "23"}, {"size": 7265, "mtime": 1751021439578, "results": "26", "hashOfConfig": "23"}, {"size": 710, "mtime": 1750766591000, "results": "27", "hashOfConfig": "23"}, {"size": 3936, "mtime": 1751033680581, "results": "28", "hashOfConfig": "23"}, {"size": 378, "mtime": 1750766591000, "results": "29", "hashOfConfig": "23"}, {"size": 8486, "mtime": 1751033566426, "results": "30", "hashOfConfig": "23"}, {"size": 1770, "mtime": 1750766591000, "results": "31", "hashOfConfig": "23"}, {"size": 3068, "mtime": 1750766591000, "results": "32", "hashOfConfig": "23"}, {"size": 786, "mtime": 1751022541809, "results": "33", "hashOfConfig": "23"}, {"size": 843, "mtime": 1750766591000, "results": "34", "hashOfConfig": "23"}, {"size": 1967, "mtime": 1751033840050, "results": "35", "hashOfConfig": "23"}, {"size": 1806, "mtime": 1751023563768, "results": "36", "hashOfConfig": "23"}, {"size": 750, "mtime": 1750766591000, "results": "37", "hashOfConfig": "23"}, {"size": 1289, "mtime": 1750766591000, "results": "38", "hashOfConfig": "23"}, {"size": 259, "mtime": 1750766591000, "results": "39", "hashOfConfig": "23"}, {"size": 13908, "mtime": 1750793313528, "results": "40", "hashOfConfig": "23"}, {"size": 540, "mtime": 1751021452555, "results": "41", "hashOfConfig": "23"}, {"size": 2179, "mtime": 1751033566426, "results": "42", "hashOfConfig": "23"}, {"size": 3588, "mtime": 1751034503171, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fii283", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\JobDetail.js", ["107"], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Header.js", ["108"], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ProjectImageSwiper.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\NDANotification.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\IntroCrafting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Statistics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\SkillsTicker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Experience.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Portfolio.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ClientThoughts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\Intro.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\data\\jobsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\ScrollToTop.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\AdminLogin.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio\\portfolio-react\\src\\components\\AdminDashboard.js", [], [], {"ruleId": "109", "severity": 1, "message": "110", "line": 1, "column": 27, "nodeType": "111", "messageId": "112", "endLine": 1, "endColumn": 34}, {"ruleId": "109", "severity": 1, "message": "113", "line": 2, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 2, "endColumn": 14}, "no-unused-vars", "'useMemo' is defined but never used.", "Identifier", "unusedVar", "'Link' is defined but never used."]