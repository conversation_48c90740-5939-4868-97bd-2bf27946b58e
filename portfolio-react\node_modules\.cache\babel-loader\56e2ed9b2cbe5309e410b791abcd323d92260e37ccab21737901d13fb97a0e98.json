{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio\\\\portfolio-react\\\\src\\\\components\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './AdminDashboard.css';\nimport { FaU<PERSON>s, <PERSON>aEye, FaChartPie, FaListUl } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    const fetchStats = async () => {\n      const token = localStorage.getItem('token');\n      if (!token) {\n        setError('No token found. Please log in.');\n        return;\n      }\n      try {\n        const response = await fetch('http://localhost:5000/api/admin/dashboard', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n        const data = await response.json();\n        if (response.ok) {\n          setStats(data);\n        } else {\n          setError(data.message || 'Failed to fetch stats');\n        }\n      } catch (err) {\n        setError('Network error');\n      }\n    };\n    fetchStats();\n  }, []);\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-error\",\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 21\n  }, this);\n  if (!stats) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-loading\",\n    children: \"Loading dashboard...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 22\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"dashboard-title\",\n      children: [/*#__PURE__*/_jsxDEV(FaUsers, {\n        style: {\n          marginRight: 12,\n          color: '#FF2D55'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 39\n      }, this), \"Admin Dashboard\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-stats-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(FaEye, {\n          className: \"dashboard-stat-icon\",\n          style: {\n            color: '#4B0082'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-stat-label\",\n          children: \"Total Visits\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-stat-value\",\n          children: stats.totalVisits\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-stat-card dashboard-section-stats\",\n        children: [/*#__PURE__*/_jsxDEV(FaChartPie, {\n          className: \"dashboard-stat-icon\",\n          style: {\n            color: '#FF2D55'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-stat-label\",\n          children: \"Section Stats\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-section-stats-grid\",\n          children: stats.sectionStats.map((s, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-section-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"section-name\",\n              children: s.section\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"section-count\",\n              children: [s.count, \" views\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"section-percent\",\n              children: [\"(\", s.percent, \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this)]\n          }, i, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-stat-card dashboard-visitor-log\",\n        children: [/*#__PURE__*/_jsxDEV(FaListUl, {\n          className: \"dashboard-stat-icon\",\n          style: {\n            color: '#4B0082'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-stat-label\",\n          children: \"Visitor Log\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-visitor-log-list\",\n          children: stats.visits.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-empty\",\n            children: \"No visits yet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: stats.visits.map((v, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visitor-timestamp\",\n                children: new Date(v.timestamp).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visitor-ip\",\n                children: v.ip\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visitor-section\",\n                children: v.section\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 21\n              }, this)]\n            }, i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"EbJQS5GpWNuLdr0Eg+eRk+n8dU0=\");\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "FaUsers", "FaEye", "FaChart<PERSON>ie", "FaListUl", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "stats", "setStats", "error", "setError", "fetchStats", "token", "localStorage", "getItem", "response", "fetch", "headers", "data", "json", "ok", "message", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "color", "totalVisits", "sectionStats", "map", "s", "i", "section", "count", "percent", "visits", "length", "v", "Date", "timestamp", "toLocaleString", "ip", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio/portfolio-react/src/components/AdminDashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport './AdminDashboard.css';\r\nimport { FaU<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, <PERSON>aC<PERSON><PERSON><PERSON>, FaListUl } from 'react-icons/fa';\r\n\r\nconst AdminDashboard = () => {\r\n  const [stats, setStats] = useState(null);\r\n  const [error, setError] = useState('');\r\n\r\n  useEffect(() => {\r\n    const fetchStats = async () => {\r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        setError('No token found. Please log in.');\r\n        return;\r\n      }\r\n      try {\r\n        const response = await fetch('http://localhost:5000/api/admin/dashboard', {\r\n          headers: { 'Authorization': `Bearer ${token}` }\r\n        });\r\n        const data = await response.json();\r\n        if (response.ok) {\r\n          setStats(data);\r\n        } else {\r\n          setError(data.message || 'Failed to fetch stats');\r\n        }\r\n      } catch (err) {\r\n        setError('Network error');\r\n      }\r\n    };\r\n    fetchStats();\r\n  }, []);\r\n\r\n  if (error) return <div className=\"dashboard-error\">{error}</div>;\r\n  if (!stats) return <div className=\"dashboard-loading\">Loading dashboard...</div>;\r\n\r\n  return (\r\n    <div className=\"dashboard-container\">\r\n      <h2 className=\"dashboard-title\"><FaUsers style={{marginRight: 12, color: '#FF2D55'}}/>Admin Dashboard</h2>\r\n      <div className=\"dashboard-stats-grid\">\r\n        <div className=\"dashboard-stat-card\">\r\n          <FaEye className=\"dashboard-stat-icon\" style={{color: '#4B0082'}}/>\r\n          <div className=\"dashboard-stat-label\">Total Visits</div>\r\n          <div className=\"dashboard-stat-value\">{stats.totalVisits}</div>\r\n        </div>\r\n        <div className=\"dashboard-stat-card dashboard-section-stats\">\r\n          <FaChartPie className=\"dashboard-stat-icon\" style={{color: '#FF2D55'}}/>\r\n          <div className=\"dashboard-stat-label\">Section Stats</div>\r\n          <div className=\"dashboard-section-stats-grid\">\r\n            {stats.sectionStats.map((s, i) => (\r\n              <div className=\"dashboard-section-stat\" key={i}>\r\n                <span className=\"section-name\">{s.section}</span>\r\n                <span className=\"section-count\">{s.count} views</span>\r\n                <span className=\"section-percent\">({s.percent}%)</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n        <div className=\"dashboard-stat-card dashboard-visitor-log\">\r\n          <FaListUl className=\"dashboard-stat-icon\" style={{color: '#4B0082'}}/>\r\n          <div className=\"dashboard-stat-label\">Visitor Log</div>\r\n          <div className=\"dashboard-visitor-log-list\">\r\n            {stats.visits.length === 0 ? (\r\n              <div className=\"dashboard-empty\">No visits yet.</div>\r\n            ) : (\r\n              <ul>\r\n                {stats.visits.map((v, i) => (\r\n                  <li key={i}>\r\n                    <span className=\"visitor-timestamp\">{new Date(v.timestamp).toLocaleString()}</span>\r\n                    <span className=\"visitor-ip\">{v.ip}</span>\r\n                    <span className=\"visitor-section\">{v.section}</span>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AdminDashboard; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,sBAAsB;AAC7B,SAASC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACd,MAAMc,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVF,QAAQ,CAAC,gCAAgC,CAAC;QAC1C;MACF;MACA,IAAI;QACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,2CAA2C,EAAE;UACxEC,OAAO,EAAE;YAAE,eAAe,EAAE,UAAUL,KAAK;UAAG;QAChD,CAAC,CAAC;QACF,MAAMM,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,IAAIJ,QAAQ,CAACK,EAAE,EAAE;UACfZ,QAAQ,CAACU,IAAI,CAAC;QAChB,CAAC,MAAM;UACLR,QAAQ,CAACQ,IAAI,CAACG,OAAO,IAAI,uBAAuB,CAAC;QACnD;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZZ,QAAQ,CAAC,eAAe,CAAC;MAC3B;IACF,CAAC;IACDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIF,KAAK,EAAE,oBAAOL,OAAA;IAAKmB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,EAAEf;EAAK;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAChE,IAAI,CAACrB,KAAK,EAAE,oBAAOH,OAAA;IAAKmB,SAAS,EAAC,mBAAmB;IAAAC,QAAA,EAAC;EAAoB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAEhF,oBACExB,OAAA;IAAKmB,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCpB,OAAA;MAAImB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAACpB,OAAA,CAACL,OAAO;QAAC8B,KAAK,EAAE;UAACC,WAAW,EAAE,EAAE;UAAEC,KAAK,EAAE;QAAS;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,mBAAe;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1GxB,OAAA;MAAKmB,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCpB,OAAA;QAAKmB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClCpB,OAAA,CAACJ,KAAK;UAACuB,SAAS,EAAC,qBAAqB;UAACM,KAAK,EAAE;YAACE,KAAK,EAAE;UAAS;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACnExB,OAAA;UAAKmB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxDxB,OAAA;UAAKmB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEjB,KAAK,CAACyB;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNxB,OAAA;QAAKmB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1DpB,OAAA,CAACH,UAAU;UAACsB,SAAS,EAAC,qBAAqB;UAACM,KAAK,EAAE;YAACE,KAAK,EAAE;UAAS;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACxExB,OAAA;UAAKmB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzDxB,OAAA;UAAKmB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAC1CjB,KAAK,CAAC0B,YAAY,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBAC3BhC,OAAA;YAAKmB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCpB,OAAA;cAAMmB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEW,CAAC,CAACE;YAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDxB,OAAA;cAAMmB,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAEW,CAAC,CAACG,KAAK,EAAC,QAAM;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDxB,OAAA;cAAMmB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,GAAC,EAACW,CAAC,CAACI,OAAO,EAAC,IAAE;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAHZQ,CAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIzC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxB,OAAA;QAAKmB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDpB,OAAA,CAACF,QAAQ;UAACqB,SAAS,EAAC,qBAAqB;UAACM,KAAK,EAAE;YAACE,KAAK,EAAE;UAAS;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,eACtExB,OAAA;UAAKmB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvDxB,OAAA;UAAKmB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACxCjB,KAAK,CAACiC,MAAM,CAACC,MAAM,KAAK,CAAC,gBACxBrC,OAAA;YAAKmB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAErDxB,OAAA;YAAAoB,QAAA,EACGjB,KAAK,CAACiC,MAAM,CAACN,GAAG,CAAC,CAACQ,CAAC,EAAEN,CAAC,kBACrBhC,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAMmB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAE,IAAImB,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,CAACC,cAAc,CAAC;cAAC;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnFxB,OAAA;gBAAMmB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEkB,CAAC,CAACI;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1CxB,OAAA;gBAAMmB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEkB,CAAC,CAACL;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAH7CQ,CAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIN,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QACL;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA3EID,cAAc;AAAA0C,EAAA,GAAd1C,cAAc;AA6EpB,eAAeA,cAAc;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}