/* Admin Dashboard Styles - Inspired by Portfolio Theme */
.dashboard-container {
  max-width: 1200px;
  margin: 40px auto;
  padding: 40px 20px;
  background: linear-gradient(135deg, rgba(75,0,130,0.15) 0%, rgba(255,45,85,0.10) 100%);
  border-radius: 30px;
  box-shadow: 0 8px 32px rgba(75,0,130,0.18), 0 4px 16px rgba(255,45,85,0.10);
  font-family: 'Montserrat', sans-serif;
  color: #fff;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 800;
  letter-spacing: 2px;
  margin-bottom: 40px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  display: flex;
  align-items: center;
  gap: 12px;
}

.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 32px;
}

.dashboard-stat-card {
  background: rgba(0,0,0,0.7);
  border-radius: 20px;
  box-shadow: 0 4px 24px rgba(75,0,130,0.15), 0 2px 8px rgba(255,45,85,0.10);
  padding: 32px 28px 28px 28px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: 220px;
  position: relative;
  overflow: hidden;
  border: 1.5px solid rgba(255,255,255,0.10);
  transition: box-shadow 0.3s, transform 0.3s;
}

.dashboard-stat-card:hover {
  box-shadow: 0 12px 40px rgba(75,0,130,0.25), 0 6px 20px rgba(255,45,85,0.18);
  transform: translateY(-4px) scale(1.01);
}

.dashboard-stat-icon {
  font-size: 2.2rem;
  margin-bottom: 18px;
  opacity: 0.85;
}

.dashboard-stat-label {
  font-size: 1.1rem;
  font-weight: 700;
  color: #FF2D55;
  margin-bottom: 10px;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.dashboard-stat-value {
  font-size: 2.8rem;
  font-weight: 900;
  color: #4B0082;
  margin-bottom: 0;
  letter-spacing: 2px;
}

.dashboard-section-stats {
  min-height: 220px;
}

.dashboard-section-stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.dashboard-section-stat {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 1.1rem;
  background: rgba(75,0,130,0.08);
  border-radius: 12px;
  padding: 10px 16px;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(75,0,130,0.05);
}
.section-name {
  color: #FF2D55;
  font-weight: 700;
  min-width: 90px;
  text-transform: capitalize;
}
.section-count {
  color: #4B0082;
  font-weight: 700;
}
.section-percent {
  color: #fff;
  font-size: 0.98rem;
  opacity: 0.8;
}

.dashboard-visitor-log {
  min-height: 220px;
  width: 100%;
}
.dashboard-visitor-log-list {
  margin-top: 10px;
  max-height: 180px;
  overflow-y: auto;
  width: 100%;
  background: rgba(255,255,255,0.03);
  border-radius: 10px;
  padding: 8px 0 8px 0;
}
.dashboard-visitor-log-list ul {
  list-style: none;
  padding: 0 12px;
  margin: 0;
}
.dashboard-visitor-log-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  padding: 7px 0;
  border-bottom: 1px solid rgba(255,255,255,0.07);
}
.dashboard-visitor-log-list li:last-child {
  border-bottom: none;
}
.visitor-timestamp {
  color: #FF2D55;
  font-size: 0.98rem;
  min-width: 150px;
  font-weight: 600;
}
.visitor-ip {
  color: #4B0082;
  font-size: 0.98rem;
  min-width: 120px;
  font-weight: 600;
}
.visitor-section {
  color: #fff;
  font-size: 0.98rem;
  font-weight: 500;
  text-transform: capitalize;
}
.dashboard-empty {
  color: #fff;
  opacity: 0.7;
  font-style: italic;
  padding: 20px 0;
  text-align: center;
}
.dashboard-error {
  color: #FF2D55;
  background: rgba(75,0,130,0.12);
  border-radius: 12px;
  padding: 18px 30px;
  margin: 40px auto;
  max-width: 600px;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
}
.dashboard-loading {
  color: #4B0082;
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  padding: 18px 30px;
  margin: 40px auto;
  max-width: 600px;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
}

.dashboard-logout-btn {
  position: absolute;
  top: 32px;
  right: 40px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  padding: 12px 28px;
  font-size: 1.1rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(75,0,130,0.15), 0 2px 8px rgba(255,45,85,0.10);
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.3s, color 0.3s, transform 0.2s;
}
.dashboard-logout-btn:hover {
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  color: #fff;
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 8px 25px rgba(255,45,85,0.18), 0 4px 15px rgba(75,0,130,0.12);
}
@media (max-width: 900px) {
  .dashboard-stats-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  .dashboard-container {
    padding: 24px 5px;
  }
  .dashboard-logout-btn {
    top: 18px;
    right: 12px;
    padding: 10px 18px;
    font-size: 1rem;
  }
} 