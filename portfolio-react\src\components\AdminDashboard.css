/* Admin Dashboard Styles - Enhanced Portfolio Theme */
.dashboard-container {
  max-width: 1400px;
  margin: 20px auto;
  padding: 50px 30px;
  background:
    linear-gradient(135deg, rgba(75,0,130,0.2) 0%, rgba(255,45,85,0.15) 100%),
    radial-gradient(circle at 20% 20%, rgba(75,0,130,0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255,45,85,0.1) 0%, transparent 50%);
  border-radius: 35px;
  box-shadow:
    0 25px 50px rgba(75,0,130,0.25),
    0 15px 35px rgba(255,45,85,0.15),
    0 5px 15px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.1);
  font-family: 'Montserrat', sans-serif;
  color: #fff;
  position: relative;
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255,255,255,0.1);
  min-height: 90vh;
  animation: dashboardFadeIn 0.8s ease-out;
}

@keyframes dashboardFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Add floating particles effect */
.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255,45,85,0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(75,0,130,0.1) 1px, transparent 1px);
  background-size: 50px 50px, 30px 30px;
  animation: floatingParticles 20s linear infinite;
  pointer-events: none;
  border-radius: 35px;
}

@keyframes floatingParticles {
  0% { transform: translateY(0px) rotate(0deg); }
  100% { transform: translateY(-20px) rotate(360deg); }
}

.dashboard-title {
  font-size: 3rem;
  font-weight: 900;
  letter-spacing: 3px;
  margin-bottom: 50px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 50%, #4B0082 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  text-transform: uppercase;
  text-align: center;
  position: relative;
  padding: 20px 0;
}

.dashboard-title::before,
.dashboard-title::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 100px;
  height: 3px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 2px;
}

.dashboard-title::before {
  left: 0;
  transform: translateY(-50%);
}

.dashboard-title::after {
  right: 0;
  transform: translateY(-50%);
}

.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 40px;
  margin-top: 30px;
}

/* Staggered animation for stat cards */
.dashboard-stat-card:nth-child(1) {
  animation: cardSlideIn 0.6s ease-out 0.2s both;
}

.dashboard-stat-card:nth-child(2) {
  animation: cardSlideIn 0.6s ease-out 0.4s both;
}

.dashboard-stat-card:nth-child(3) {
  animation: cardSlideIn 0.6s ease-out 0.6s both;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dashboard-stat-card {
  background:
    linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(75,0,130,0.1) 100%),
    rgba(255,255,255,0.05);
  border-radius: 25px;
  box-shadow:
    0 15px 35px rgba(75,0,130,0.2),
    0 8px 20px rgba(255,45,85,0.15),
    0 3px 10px rgba(0,0,0,0.3),
    inset 0 1px 0 rgba(255,255,255,0.15);
  padding: 40px 35px 35px 35px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: 280px;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255,255,255,0.15);
  backdrop-filter: blur(15px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dashboard-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  border-radius: 25px 25px 0 0;
}

.dashboard-stat-card:hover {
  box-shadow:
    0 25px 50px rgba(75,0,130,0.35),
    0 15px 30px rgba(255,45,85,0.25),
    0 5px 15px rgba(0,0,0,0.4),
    inset 0 1px 0 rgba(255,255,255,0.2);
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(255,255,255,0.25);
}

.dashboard-stat-icon {
  font-size: 2.8rem;
  margin-bottom: 25px;
  opacity: 0.9;
  padding: 15px;
  background: rgba(255,255,255,0.1);
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
}

.dashboard-stat-card:hover .dashboard-stat-icon {
  transform: scale(1.1) rotate(5deg);
  background: rgba(255,255,255,0.15);
}

.dashboard-stat-label {
  font-size: 1.2rem;
  font-weight: 800;
  color: #FF2D55;
  margin-bottom: 15px;
  letter-spacing: 1.5px;
  text-transform: uppercase;
  position: relative;
  padding-bottom: 8px;
}

.dashboard-stat-label::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  border-radius: 1px;
}

.dashboard-stat-value {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  margin-bottom: 0;
  letter-spacing: 3px;
  text-shadow: 0 2px 4px rgba(75,0,130,0.3);
  transition: all 0.3s ease;
}

.dashboard-stat-card:hover .dashboard-stat-value {
  transform: scale(1.05);
}

.dashboard-section-stats {
  min-height: 220px;
}

.dashboard-section-stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
}

.dashboard-section-stat {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 1.1rem;
  background: rgba(75,0,130,0.08);
  border-radius: 12px;
  padding: 10px 16px;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(75,0,130,0.05);
}
.section-name {
  color: #FF2D55;
  font-weight: 700;
  min-width: 90px;
  text-transform: capitalize;
}
.section-count {
  color: #4B0082;
  font-weight: 700;
}
.section-percent {
  color: #fff;
  font-size: 0.98rem;
  opacity: 0.8;
}

.dashboard-visitor-log {
  min-height: 220px;
  width: 100%;
}
.dashboard-visitor-log-list {
  margin-top: 10px;
  max-height: 180px;
  overflow-y: auto;
  width: 100%;
  background: rgba(255,255,255,0.03);
  border-radius: 10px;
  padding: 8px 0 8px 0;
}
.dashboard-visitor-log-list ul {
  list-style: none;
  padding: 0 12px;
  margin: 0;
}
.dashboard-visitor-log-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1rem;
  padding: 7px 0;
  border-bottom: 1px solid rgba(255,255,255,0.07);
}
.dashboard-visitor-log-list li:last-child {
  border-bottom: none;
}
.visitor-timestamp {
  color: #FF2D55;
  font-size: 0.98rem;
  min-width: 150px;
  font-weight: 600;
}
.visitor-ip {
  color: #4B0082;
  font-size: 0.98rem;
  min-width: 120px;
  font-weight: 600;
}
.visitor-section {
  color: #fff;
  font-size: 0.98rem;
  font-weight: 500;
  text-transform: capitalize;
}
.dashboard-empty {
  color: #fff;
  opacity: 0.7;
  font-style: italic;
  padding: 20px 0;
  text-align: center;
}
.dashboard-error {
  color: #FF2D55;
  background: rgba(75,0,130,0.12);
  border-radius: 12px;
  padding: 18px 30px;
  margin: 40px auto;
  max-width: 600px;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
}
.dashboard-loading {
  color: #4B0082;
  background: rgba(255,255,255,0.08);
  border-radius: 12px;
  padding: 18px 30px;
  margin: 40px auto;
  max-width: 600px;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
}

.dashboard-logout-btn {
  position: absolute;
  top: 40px;
  right: 40px;
  background: linear-gradient(135deg, #FF2D55 0%, #4B0082 100%);
  color: #fff;
  border: none;
  border-radius: 30px;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-family: 'Montserrat', sans-serif;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  box-shadow:
    0 8px 25px rgba(255,45,85,0.4),
    0 4px 15px rgba(75,0,130,0.3),
    inset 0 1px 0 rgba(255,255,255,0.2);
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(15px);
  border: 2px solid rgba(255,255,255,0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.dashboard-logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.dashboard-logout-btn:hover::before {
  left: 100%;
}

.dashboard-logout-btn:hover {
  background: linear-gradient(135deg, #4B0082 0%, #FF2D55 100%);
  color: #fff;
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 15px 35px rgba(255,45,85,0.5),
    0 8px 20px rgba(75,0,130,0.4),
    inset 0 1px 0 rgba(255,255,255,0.3);
  border-color: rgba(255,255,255,0.3);
}
@media (max-width: 900px) {
  .dashboard-stats-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  .dashboard-container {
    padding: 24px 5px;
  }
  .dashboard-logout-btn {
    top: 18px;
    right: 12px;
    padding: 10px 18px;
    font-size: 1rem;
  }
} 